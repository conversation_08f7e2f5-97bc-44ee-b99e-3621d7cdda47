'use client';

import { useState, useEffect, useCallback } from 'react';
import { Boss, RealTimeMessage, BossAction } from '@/types/boss';
import { useRealTimeSharing } from './useRealTimeSharing';
import { useBossManager } from './useBossManager';

export function useCollaborativeBossManager() {
  const { isConnected, currentUser, sendMessage, addMessageHandler, isHost } = useRealTimeSharing();
  const { allBosses, customBosses, addBoss, updateBoss, deleteBoss, isCustomBoss, getBossById } = useBossManager();
  const [lastBossAction, setLastBossAction] = useState<{ userId: string; userName: string; action: string; timestamp: Date } | null>(null);

  // Handle real-time boss management messages
  useEffect(() => {
    if (!isConnected) return;

    const handleBossMessage = (message: RealTimeMessage) => {
      console.log('Received boss message:', message.type, 'from:', message.userName);
      
      switch (message.type) {
        case 'boss_add': {
          const { boss } = message.data as BossAction;
          if (boss) {
            addBoss(boss);
            setLastBossAction({
              userId: message.userId,
              userName: message.userName,
              action: `added boss ${boss.name}`,
              timestamp: new Date(message.timestamp),
            });
          }
          break;
        }

        case 'boss_update': {
          const { bossId, updates } = message.data as BossAction;
          if (bossId && updates) {
            updateBoss(bossId, updates);
            setLastBossAction({
              userId: message.userId,
              userName: message.userName,
              action: `updated boss ${updates.name || bossId}`,
              timestamp: new Date(message.timestamp),
            });
          }
          break;
        }

        case 'boss_delete': {
          const { bossId } = message.data as BossAction;
          if (bossId) {
            const boss = getBossById(bossId);
            if (deleteBoss(bossId)) {
              setLastBossAction({
                userId: message.userId,
                userName: message.userName,
                action: `deleted boss ${boss?.name || bossId}`,
                timestamp: new Date(message.timestamp),
              });
            }
          }
          break;
        }

        case 'full_sync': {
          // Receive complete boss list from host when joining
          const { customBosses: receivedBosses } = message.data as { customBosses?: Boss[] };
          if (receivedBosses && Array.isArray(receivedBosses)) {
            // Clear existing custom bosses and add received ones
            // Note: This is a simplified approach - in a real implementation,
            // you might want to merge more intelligently
            receivedBosses.forEach(boss => {
              if (!getBossById(boss.id)) {
                addBoss(boss);
              }
            });
            
            setLastBossAction({
              userId: message.userId,
              userName: message.userName,
              action: `synchronized ${receivedBosses.length} custom bosses`,
              timestamp: new Date(message.timestamp),
            });
          }
          break;
        }

        case 'user_join': {
          // Send full boss list to new user if we're the host
          if (isHost && currentUser && message.userId !== currentUser.id) {
            // Small delay to ensure the new user is ready to receive messages
            setTimeout(() => {
              sendMessage('full_sync', { 
                customBosses: customBosses
              });
            }, 1500); // Slightly longer delay than timer sync
          }
          break;
        }
      }
    };

    return addMessageHandler(handleBossMessage);
  }, [isConnected, currentUser, sendMessage, addMessageHandler, isHost, customBosses, addBoss, updateBoss, deleteBoss, getBossById]);

  // Collaborative boss management functions
  const addBossCollaborative = useCallback((boss: Boss) => {
    addBoss(boss);
    
    // Send real-time update
    if (isConnected) {
      sendMessage('boss_add', { boss });
    }
  }, [addBoss, isConnected, sendMessage]);

  const updateBossCollaborative = useCallback((bossId: string, updates: Partial<Boss>) => {
    updateBoss(bossId, updates);
    
    // Send real-time update
    if (isConnected) {
      sendMessage('boss_update', { bossId, updates });
    }
  }, [updateBoss, isConnected, sendMessage]);

  const deleteBossCollaborative = useCallback((bossId: string) => {
    const success = deleteBoss(bossId);
    
    // Send real-time update
    if (success && isConnected) {
      sendMessage('boss_delete', { bossId });
    }
    
    return success;
  }, [deleteBoss, isConnected, sendMessage]);

  return {
    // Original boss manager functionality
    allBosses,
    customBosses,
    isCustomBoss,
    getBossById,
    
    // Collaborative functions
    addBoss: addBossCollaborative,
    updateBoss: updateBossCollaborative,
    deleteBoss: deleteBossCollaborative,
    
    // Real-time state
    lastBossAction,
  };
}
