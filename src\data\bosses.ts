import { Boss } from '@/types/boss';

export const bosses: Boss[] = [
  // Gludio Region Bosses
  {
    id: 'chertuba-barracks',
    location: "Chertuba's Barracks",
    name: 'Chertuba',
    level: 40,
    respawnTime: 6,
    hp: 850000,
    minPlayers: 3,
    maxPlayers: 8,
    coordinates: { x: 120, y: 85 },
    drops: [
      { name: "Chertuba's Soul Necklace", type: 'accessory', rarity: 'rare', dropRate: 'Low' },
      { name: '<PERSON><PERSON><PERSON><PERSON>', type: 'weapon', rarity: 'rare', dropRate: 'Low' },
      { name: 'Caliburs Dual Blades', type: 'weapon', rarity: 'rare', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'Medium' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'Medium' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A powerful orc commander guarding the barracks entrance.',
    strategy: 'Focus on crowd control and maintain distance from his charge attack.',
    isActive: false,
  },
  {
    id: 'chertuba-barracks-awakened',
    location: "Chertuba's Barracks",
    name: 'Awakened Chertuba',
    level: 55,
    respawnTime: 6,
    hp: 1200000,
    minPlayers: 5,
    maxPlayers: 8,
    coordinates: { x: 120, y: 85 },
    drops: [
      { name: "Guardian's Sword", type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Crystal Gaiters', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: "Apella's Necklace", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Chertuba Tsurugi', type: 'weapon', rarity: 'rare', dropRate: 'Medium' },
      { name: 'Caliburs Dual Blades', type: 'weapon', rarity: 'rare', dropRate: 'Medium' },
      { name: 'Body Slasher', type: 'weapon', rarity: 'rare', dropRate: 'Medium' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
    ],
    description: 'The awakened form of Chertuba with enhanced abilities.',
    strategy: 'Requires coordinated team play and strong healing support.',
    isActive: false,
  },
  {
    id: 'southern-wasteland',
    location: 'Southern Wasteland',
    name: 'Basila',
    level: 40,
    respawnTime: 4,
    hp: 650000,
    minPlayers: 2,
    maxPlayers: 6,
    coordinates: { x: 200, y: 150 },
    drops: [
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'Medium' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'Medium' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A fierce beast roaming the wasteland.',
    strategy: 'Kite around rocks to avoid its pounce attack.',
    isActive: false,
  },
  {
    id: 'ruins-of-despair',
    location: 'Ruins of Despair',
    name: 'Kelsus',
    level: 40,
    respawnTime: 10,
    hp: 750000,
    minPlayers: 3,
    maxPlayers: 7,
    coordinates: { x: 180, y: 220 },
    drops: [
      { name: 'Cursed Weapon Enchant Scroll', type: 'scroll', rarity: 'rare', dropRate: 'Medium' },
      { name: 'Cursed Armor Enchant Scroll', type: 'scroll', rarity: 'rare', dropRate: 'Medium' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'An ancient guardian of the ruined temple.',
    strategy: 'Interrupt his channeling abilities to prevent massive damage.',
    isActive: false,
  },
  {
    id: 'ant-nest-b2',
    location: 'Ant Nest (B2)',
    name: 'Savan',
    level: 45,
    respawnTime: 12,
    hp: 1100000,
    minPlayers: 5,
    maxPlayers: 8,
    coordinates: { x: 250, y: 200 },
    drops: [
      { name: "Savan's Robe", type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Necklace of Immortality', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: "Desperion's Staff", type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Cursed Weapon Enchant Scroll', type: 'scroll', rarity: 'rare', dropRate: 'Medium' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Epic Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A powerful ant queen deep in the nest.',
    strategy: 'Clear adds first, then focus on the queen.',
    isActive: false,
  },
  {
    id: 'ant-nest-b3',
    location: 'Ant Nest (B3)',
    name: 'Queen Ant',
    level: 50,
    respawnTime: 6,
    hp: 2000000,
    minPlayers: 6,
    maxPlayers: 12,
    coordinates: { x: 75, y: 200 },
    drops: [
      { name: "Queen Ant's Wings", type: 'material', rarity: 'legendary', dropRate: 'Very Low' },
      { name: 'Majestic Robe', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Majestic Gloves', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Majestic Boots', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Majestic Keshanberk', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Ring of Blessing', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Accessory Enchant Scroll', type: 'scroll', rarity: 'rare', dropRate: 'Medium' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
    ],
    description: 'The massive queen of the ant colony with devastating area attacks.',
    strategy: 'Requires raid coordination. Focus on adds management and positioning.',
    isActive: false,
  },
  {
    id: 'bloodstained-swampland',
    location: 'Bloodstained Swampland',
    name: 'Tromba',
    level: 60,
    respawnTime: 7,
    hp: 1500000,
    minPlayers: 4,
    maxPlayers: 8,
    coordinates: { x: 95, y: 180 },
    drops: [
      { name: 'Damascus Dual Blades', type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: "Demon's Orb", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blue Wolf Helmet', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A massive swamp creature with toxic abilities.',
    strategy: 'Bring poison resistance and focus on mobility.',
    isActive: false,
  },
  // Dion Region Bosses
  {
    id: 'bee-hive',
    location: 'Bee Hive',
    name: 'Felis',
    level: 40,
    respawnTime: 3,
    hp: 500000,
    minPlayers: 2,
    maxPlayers: 5,
    coordinates: { x: 160, y: 90 },
    drops: [
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'Medium' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'Medium' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Archery Manual (Ultimate Evasion)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: "Priest's Records (Concentration)", type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A swift feline predator with high mobility.',
    strategy: 'Quick fight, but watch for his leap attacks.',
    isActive: false,
  },
  {
    id: 'rebel-territory',
    location: 'Rebel Territory',
    name: 'Talakin',
    level: 40,
    respawnTime: 10,
    hp: 800000,
    minPlayers: 3,
    maxPlayers: 7,
    coordinates: { x: 140, y: 170 },
    drops: [
      { name: 'Full Plate Armor', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Full Plate Gauntlets', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Sword of Nightmares', type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Crystal Dagger', type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Crystallized Ice Bow', type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Soul Separator', type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
    ],
    description: 'Leader of the rebel forces with tactical combat skills.',
    strategy: 'Disrupt his formations and focus fire to prevent reinforcements.',
    isActive: false,
  },
  {
    id: 'dion-plains',
    location: 'Dion Plains',
    name: 'Enkura',
    level: 40,
    respawnTime: 6,
    hp: 600000,
    minPlayers: 2,
    maxPlayers: 6,
    coordinates: { x: 220, y: 130 },
    drops: [
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'Medium' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'Medium' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Battle Tome (Defense Aura)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Art of Dual Blades (Sonic Blaster)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A territorial beast protecting the plains.',
    strategy: 'Straightforward fight, maintain distance from charge attacks.',
    isActive: false,
  },
  {
    id: 'dion-hills',
    location: 'Dion Hills',
    name: "Pan Dra'eed",
    level: 40,
    respawnTime: 12,
    hp: 900000,
    minPlayers: 4,
    maxPlayers: 8,
    coordinates: { x: 240, y: 110 },
    drops: [
      { name: "Pan Dra'eed's Necklace", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Drake Leather Boots', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Archery Manual (Energize)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Textbook of Magic (Cancellation)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'An ancient mage with powerful elemental magic.',
    strategy: 'Interrupt his spell casting and use magic resistance.',
    isActive: false,
  },
  // Cruma Region Bosses
  {
    id: 'cruma-marshlands',
    location: 'Cruma Marshlands',
    name: 'Mutated Cruma',
    level: 50,
    respawnTime: 8,
    hp: 1200000,
    minPlayers: 4,
    maxPlayers: 8,
    coordinates: { x: 300, y: 200 },
    drops: [
      { name: "Cruma's Horn", type: 'material', rarity: 'epic', dropRate: 'Low' },
      { name: "Cruma's Shell", type: 'material', rarity: 'epic', dropRate: 'Low' },
      { name: 'Ring of Passion', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Damascus Dual Blades', type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Epic Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A mutated creature with enhanced abilities.',
    strategy: 'Focus on positioning and avoid its toxic attacks.',
    isActive: false,
  },
  {
    id: 'morgue',
    location: 'Morgue',
    name: 'Tempest',
    level: 45,
    respawnTime: 6,
    hp: 950000,
    minPlayers: 3,
    maxPlayers: 7,
    coordinates: { x: 110, y: 60 },
    drops: [
      { name: "Demon's Orb", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: "Devil's Pact", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Vilesoil Valefar Necklace', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Touch of Life', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'An undead warrior with lightning-fast attacks.',
    strategy: 'Use holy magic and maintain high mobility.',
    isActive: false,
  },
  {
    id: 'cruma-tower-b4',
    location: 'Cruma Tower (B4)',
    name: 'Contaminated Cruma',
    level: 45,
    respawnTime: 8,
    hp: 1000000,
    minPlayers: 4,
    maxPlayers: 8,
    coordinates: { x: 320, y: 180 },
    drops: [
      { name: 'Spear Training (Absolute Spear)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Cloak of Authority', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: "Tiat's Belt", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Resist', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Greatsword Techniques (Reflect Sun)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Archery Manual (Elemental Spike)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
    ],
    description: 'A contaminated creature with enhanced toxic abilities.',
    strategy: 'Requires strong poison resistance and coordinated attacks.',
    isActive: false,
  },
  {
    id: 'cruma-tower-b6',
    location: 'Cruma Tower (B6)',
    name: 'Katan',
    level: 55,
    respawnTime: 10,
    hp: 1400000,
    minPlayers: 5,
    maxPlayers: 8,
    coordinates: { x: 340, y: 160 },
    drops: [
      { name: 'Feather Eye Buster', type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Art of Dual Blades (Breaking Armor)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Boots of Moonsouls Cloak', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: "Baium's Necklace", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: "Inheritor's Book (Increase Dual Blades)", type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Sonic Mastery', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Crossbow Guidebook (Chain Strike)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Blessing', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Blue Wolf Gaiters', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Inferno Ring', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Belt of Blessing', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
    ],
    description: 'A powerful guardian of the tower depths.',
    strategy: 'High-level coordination required, focus on interrupting abilities.',
    isActive: false,
  },
  {
    id: 'cruma-tower-b7',
    location: 'Cruma Tower (B7)',
    name: 'Core Susceptor',
    level: 60,
    respawnTime: 10,
    hp: 1600000,
    minPlayers: 6,
    maxPlayers: 8,
    coordinates: { x: 360, y: 140 },
    drops: [
      { name: 'Sarunga', type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: "Lord's Authority", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: "Susceptor's Heart", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Core Ring', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Dark Crystal Gloves', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Dark Crystal Boots', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Dark Crystal Helmet', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
    ],
    description: 'The core guardian of Cruma Tower with immense power.',
    strategy: 'Requires maximum coordination and high-level equipment.',
    isActive: false,
  },
  // Delu Dwellings
  {
    id: 'delu-dwellings',
    location: 'Delu Dwellings',
    name: 'Sarka',
    level: 45,
    respawnTime: 10,
    hp: 850000,
    minPlayers: 3,
    maxPlayers: 7,
    coordinates: { x: 190, y: 250 },
    drops: [
      { name: 'Full Plate Gaiters', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Full Plate Boots', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A tribal shaman with nature-based magic.',
    strategy: 'Dispel his buffs and avoid standing in nature traps.',
    isActive: false,
  },
  // Giran Region Bosses
  {
    id: 'floran-fields',
    location: 'Floran Fields',
    name: 'Timitris',
    level: 40,
    respawnTime: 8,
    hp: 700000,
    minPlayers: 3,
    maxPlayers: 6,
    coordinates: { x: 260, y: 140 },
    drops: [
      { name: 'Blue Wolf Breastplate', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blue Wolf Gloves', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Battle Tome (Detect Weakness)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Art of Dual Blades (Detect Weakness)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A plant-like creature that controls the field vegetation.',
    strategy: 'Use fire magic and clear the area of plant minions first.',
    isActive: false,
  },
  {
    id: 'giants-vestige',
    location: "Giants' Vestige",
    name: 'Stonegeist',
    level: 45,
    respawnTime: 7,
    hp: 1000000,
    minPlayers: 4,
    maxPlayers: 8,
    coordinates: { x: 300, y: 80 },
    drops: [
      { name: "Demon's Dagger", type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: "Stakato Queen's Staff", type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blood Gaiters', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Epic Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Assassination Bible (Venom)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Archery Manual (Rapid Shot)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Assassination Bible (Shadow Blade)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A massive stone golem left by ancient giants.',
    strategy: 'Focus on weak points and use earth-shattering abilities.',
    isActive: false,
  },
  {
    id: 'tanor-canyon',
    location: 'Tanor Canyon',
    name: 'Gahareth',
    level: 70,
    respawnTime: 9,
    hp: 1800000,
    minPlayers: 6,
    maxPlayers: 8,
    coordinates: { x: 400, y: 120 },
    drops: [
      { name: "Dark Legion's Edge", type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Majestic Ring', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Bracelet of Blessing', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Belt of Blessing', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Accessory Enchant Scroll', type: 'scroll', rarity: 'rare', dropRate: 'Medium' },
      { name: 'Epic Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Mana Seeker', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A powerful demon lord commanding the canyon.',
    strategy: 'Requires high-level coordination and strong defensive abilities.',
    isActive: false,
  },
  {
    id: 'medusa-garden',
    location: 'Medusa Garden',
    name: 'Medusa',
    level: 55,
    respawnTime: 10,
    hp: 1300000,
    minPlayers: 5,
    maxPlayers: 8,
    coordinates: { x: 380, y: 100 },
    drops: [
      { name: "Medusa's Helm", type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: "Medusa's Cloak", type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Themo Tongue', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Epic Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: "Priest's Records (Arcane Shield)", type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Art of Dual Blades (Triple Slash)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'The legendary Medusa with petrifying gaze.',
    strategy: 'Avoid direct eye contact and use ranged attacks.',
    isActive: false,
  },
  {
    id: 'death-pass',
    location: 'Death Pass',
    name: 'Black Lily',
    level: 65,
    respawnTime: 12,
    hp: 1700000,
    minPlayers: 6,
    maxPlayers: 8,
    coordinates: { x: 420, y: 80 },
    drops: [
      { name: "Cabrio's Hand", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: "Demon's Boots", type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: "Black Lily's Magic Necklace", type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Accessory Enchant Scroll', type: 'scroll', rarity: 'rare', dropRate: 'Medium' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Epic Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Assassination Bible (Judgment)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Textbook of Magic (Restore Casting)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: "Priest's Records (Judgment)", type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Textbook of Magic (Critical Magic)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Assassination Bible (Focus Accuracy)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A dark sorceress with deadly magic.',
    strategy: 'High magic resistance required, interrupt her casting.',
    isActive: false,
  },
  // Aden Region Bosses
  {
    id: 'pillagers-outpost',
    location: "Pillagers' Outpost",
    name: 'Matura',
    level: 50,
    respawnTime: 6,
    hp: 1200000,
    minPlayers: 4,
    maxPlayers: 8,
    coordinates: { x: 450, y: 150 },
    drops: [
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Assassination Bible (Deadly Blow)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Art of Dual Blades (Double Slash)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'Leader of the pillager forces.',
    strategy: 'Fast-paced combat, focus on mobility.',
    isActive: false,
  },
  {
    id: 'brekas-stronghold',
    location: "Breka's Stronghold",
    name: 'Breka',
    level: 50,
    respawnTime: 6,
    hp: 1200000,
    minPlayers: 4,
    maxPlayers: 8,
    coordinates: { x: 470, y: 130 },
    drops: [
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Battle Tome (Deflect Arrow)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Art of Dual Blades (Double Slash)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A fortified stronghold commander.',
    strategy: 'Siege-style combat, coordinate attacks on weak points.',
    isActive: false,
  },
  // Oren Region Bosses
  {
    id: 'gorgon-flower-garden',
    location: 'Gorgon Flower Garden',
    name: 'Pan Marrod',
    level: 50,
    respawnTime: 5,
    hp: 1100000,
    minPlayers: 4,
    maxPlayers: 8,
    coordinates: { x: 500, y: 180 },
    drops: [
      { name: 'Akai Longbow', type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Battle Tome (Ultimate Defense)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A nature guardian with plant-based abilities.',
    strategy: 'Use fire attacks and clear minions quickly.',
    isActive: false,
  },
  {
    id: 'dragon-valley-north',
    location: 'Dragon Valley (North)',
    name: 'Behemoth',
    level: 65,
    respawnTime: 9,
    hp: 1600000,
    minPlayers: 6,
    maxPlayers: 8,
    coordinates: { x: 520, y: 200 },
    drops: [
      { name: 'Sword of Miracles', type: 'weapon', rarity: 'epic', dropRate: 'Low' },
      { name: 'Gaiters', type: 'armor', rarity: 'epic', dropRate: 'Low' },
      { name: 'Behemoth Leather Belt', type: 'accessory', rarity: 'epic', dropRate: 'Low' },
      { name: 'Blessed Weapon Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Armor Enchant Scroll', type: 'scroll', rarity: 'common', dropRate: 'High' },
      { name: 'Blessed Life Stone', type: 'material', rarity: 'common', dropRate: 'High' },
      { name: 'Epic Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Unique Craft Recipe', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Magic Lizard Storm', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Battle Tome (Double Shock)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
      { name: 'Archery Manual (Scooter Stance)', type: 'other', rarity: 'epic', dropRate: 'Very Low' },
    ],
    description: 'A massive dragon-like creature with devastating attacks.',
    strategy: 'Requires maximum coordination and dragon-slaying equipment.',
    isActive: false,
  },
];
