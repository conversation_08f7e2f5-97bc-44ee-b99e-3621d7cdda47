'use client';

import { useRealTimeSharing } from '@/hooks/useRealTimeSharing';

interface ConnectionStatusProps {
  lastAction?: { userId: string; userName: string; action: string; timestamp: Date } | null;
  lastBossAction?: { userId: string; userName: string; action: string; timestamp: Date } | null;
}

export default function ConnectionStatus({ lastAction, lastBossAction }: ConnectionStatusProps) {
  const { isConnected, roomId, connectedUsers, currentUser } = useRealTimeSharing();

  if (!isConnected) return null;

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - timestamp.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    
    if (diffSeconds < 60) {
      return 'just now';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else {
      const diffHours = Math.floor(diffMinutes / 60);
      return `${diffHours}h ago`;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 space-y-3">
      {/* Connection Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            Real-Time Room
          </span>
        </div>
        <code className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded text-xs font-mono">
          {roomId}
        </code>
      </div>

      {/* Connected Users */}
      <div className="flex items-center gap-2">
        <span className="text-xs text-gray-600 dark:text-gray-400">Connected:</span>
        <div className="flex items-center gap-1">
          {connectedUsers.slice(0, 5).map((user) => (
            <div
              key={user.id}
              className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium"
              style={{ backgroundColor: user.color }}
              title={user.name}
            >
              {user.name.charAt(0).toUpperCase()}
            </div>
          ))}
          {connectedUsers.length > 5 && (
            <div className="w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center text-white text-xs font-medium">
              +{connectedUsers.length - 5}
            </div>
          )}
        </div>
        <span className="text-xs text-gray-500 dark:text-gray-400">
          ({connectedUsers.length} user{connectedUsers.length !== 1 ? 's' : ''})
        </span>
      </div>

      {/* Last Actions */}
      {(lastAction || lastBossAction) && (
        <div className="space-y-2">
          {lastAction && lastAction.userId !== currentUser?.id && (
            <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-2">
              <div className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{
                    backgroundColor: connectedUsers.find(u => u.id === lastAction.userId)?.color || '#6B7280'
                  }}
                ></div>
                <span className="text-xs text-blue-700 dark:text-blue-300">
                  <strong>{lastAction.userName}</strong> {lastAction.action}
                </span>
                <span className="text-xs text-blue-600 dark:text-blue-400 ml-auto">
                  {formatTimeAgo(lastAction.timestamp)}
                </span>
              </div>
            </div>
          )}

          {lastBossAction && lastBossAction.userId !== currentUser?.id && (
            <div className="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-2">
              <div className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{
                    backgroundColor: connectedUsers.find(u => u.id === lastBossAction.userId)?.color || '#6B7280'
                  }}
                ></div>
                <span className="text-xs text-green-700 dark:text-green-300">
                  <strong>{lastBossAction.userName}</strong> {lastBossAction.action}
                </span>
                <span className="text-xs text-green-600 dark:text-green-400 ml-auto">
                  {formatTimeAgo(lastBossAction.timestamp)}
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      <div className="text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-600 pt-2">
        Timer and boss changes sync automatically with all connected users
      </div>
    </div>
  );
}
