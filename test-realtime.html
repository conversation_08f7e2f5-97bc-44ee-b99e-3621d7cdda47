<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-Time Sharing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .primary { background-color: #007bff; color: white; }
        .secondary { background-color: #6c757d; color: white; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Real-Time Sharing Test</h1>
    
    <div class="container">
        <h2>Instructions</h2>
        <ol>
            <li>Open this page in two different browser windows/tabs</li>
            <li>In the first window, click "Create Room" and enter a username</li>
            <li>Copy the Room ID that appears</li>
            <li>In the second window, click "Join Room", enter the Room ID and a different username</li>
            <li>Test sending messages between the windows</li>
        </ol>
    </div>

    <div class="container">
        <h2>Connection Status</h2>
        <div id="status" class="status disconnected">Disconnected</div>
        <div id="roomInfo" style="display: none;">
            <p><strong>Room ID:</strong> <span id="roomId"></span></p>
            <p><strong>User:</strong> <span id="userName"></span></p>
            <p><strong>Connected Users:</strong> <span id="connectedUsers"></span></p>
        </div>
    </div>

    <div class="container">
        <h2>Actions</h2>
        <div id="disconnectedActions">
            <input type="text" id="createUserName" placeholder="Enter your username" />
            <button class="primary" onclick="createRoom()">Create Room</button>
            <br><br>
            <input type="text" id="joinRoomId" placeholder="Enter Room ID" />
            <input type="text" id="joinUserName" placeholder="Enter your username" />
            <button class="primary" onclick="joinRoom()">Join Room</button>
        </div>
        <div id="connectedActions" style="display: none;">
            <input type="text" id="messageText" placeholder="Enter test message" />
            <button class="primary" onclick="sendTestMessage()">Send Test Message</button>
            <button class="secondary" onclick="leaveRoom()">Leave Room</button>
        </div>
    </div>

    <div class="container">
        <h2>Message Log</h2>
        <div id="messageLog" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        // Simple WebRTC test implementation
        let isConnected = false;
        let roomId = null;
        let currentUser = null;
        let connectedUsers = [];
        
        function log(message) {
            const logElement = document.getElementById('messageLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateUI() {
            const statusElement = document.getElementById('status');
            const roomInfoElement = document.getElementById('roomInfo');
            const disconnectedActions = document.getElementById('disconnectedActions');
            const connectedActions = document.getElementById('connectedActions');

            if (isConnected) {
                statusElement.textContent = 'Connected';
                statusElement.className = 'status connected';
                roomInfoElement.style.display = 'block';
                disconnectedActions.style.display = 'none';
                connectedActions.style.display = 'block';
                
                document.getElementById('roomId').textContent = roomId;
                document.getElementById('userName').textContent = currentUser?.name || '';
                document.getElementById('connectedUsers').textContent = connectedUsers.length;
            } else {
                statusElement.textContent = 'Disconnected';
                statusElement.className = 'status disconnected';
                roomInfoElement.style.display = 'none';
                disconnectedActions.style.display = 'block';
                connectedActions.style.display = 'none';
            }
        }

        function createRoom() {
            const userName = document.getElementById('createUserName').value.trim();
            if (!userName) {
                alert('Please enter a username');
                return;
            }

            // Simulate room creation
            roomId = Math.random().toString(36).substring(2, 8).toUpperCase();
            currentUser = { id: Math.random().toString(36).substring(2, 15), name: userName };
            connectedUsers = [currentUser];
            isConnected = true;

            // Store in localStorage to simulate the real implementation
            localStorage.setItem(`room-${roomId}`, JSON.stringify({
                users: connectedUsers,
                signals: [],
                lastUpdated: Date.now()
            }));

            log(`Room created: ${roomId}`);
            log(`User ${userName} joined as host`);
            updateUI();
        }

        function joinRoom() {
            const joinRoomId = document.getElementById('joinRoomId').value.trim();
            const userName = document.getElementById('joinUserName').value.trim();
            
            if (!joinRoomId || !userName) {
                alert('Please enter both Room ID and username');
                return;
            }

            // Check if room exists
            const roomData = localStorage.getItem(`room-${joinRoomId}`);
            if (!roomData) {
                alert('Room not found');
                return;
            }

            try {
                const parsedRoomData = JSON.parse(roomData);
                roomId = joinRoomId;
                currentUser = { id: Math.random().toString(36).substring(2, 15), name: userName };
                connectedUsers = [...parsedRoomData.users, currentUser];
                isConnected = true;

                // Update room data
                localStorage.setItem(`room-${roomId}`, JSON.stringify({
                    users: connectedUsers,
                    signals: parsedRoomData.signals || [],
                    lastUpdated: Date.now()
                }));

                log(`Joined room: ${roomId}`);
                log(`User ${userName} joined`);
                updateUI();
            } catch (error) {
                alert('Failed to join room');
                log(`Error joining room: ${error.message}`);
            }
        }

        function sendTestMessage() {
            const messageText = document.getElementById('messageText').value.trim();
            if (!messageText) {
                alert('Please enter a message');
                return;
            }

            log(`Sending message: "${messageText}"`);
            
            // In the real implementation, this would go through WebRTC
            // For testing, we'll just log it
            setTimeout(() => {
                log(`Message sent successfully`);
            }, 100);

            document.getElementById('messageText').value = '';
        }

        function leaveRoom() {
            if (roomId && currentUser) {
                // Remove user from room data
                try {
                    const roomData = localStorage.getItem(`room-${roomId}`);
                    if (roomData) {
                        const parsedRoomData = JSON.parse(roomData);
                        const updatedUsers = parsedRoomData.users.filter(u => u.id !== currentUser.id);
                        localStorage.setItem(`room-${roomId}`, JSON.stringify({
                            users: updatedUsers,
                            signals: parsedRoomData.signals || [],
                            lastUpdated: Date.now()
                        }));
                    }
                } catch (error) {
                    log(`Error updating room data: ${error.message}`);
                }

                log(`User ${currentUser.name} left room ${roomId}`);
            }

            roomId = null;
            currentUser = null;
            connectedUsers = [];
            isConnected = false;
            updateUI();
        }

        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
        }

        // Initialize UI
        updateUI();
        log('Test page loaded. Ready to test real-time sharing.');
    </script>
</body>
</html>
