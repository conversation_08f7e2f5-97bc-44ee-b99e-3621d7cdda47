# Netlify Deployment Guide for L2 Boss Timer

## Prerequisites

1. **Netlify Account**: Sign up at [netlify.com](https://netlify.com)
2. **Git Repository**: Your code should be in a Git repository (GitHub, GitLab, etc.)
3. **Node.js**: Ensure you have Node.js 18+ installed

## Deployment Methods

### Method 1: Git Integration (Recommended)

1. **Push your code to a Git repository** (GitHub, GitLab, Bitbucket)

2. **Connect to Netlify**:
   - Go to [Netlify Dashboard](https://app.netlify.com)
   - Click "New site from Git"
   - Choose your Git provider and repository
   - Configure build settings:
     - **Build command**: `npm run build`
     - **Publish directory**: `out`
     - **Node version**: `18`

3. **Deploy**: Netlify will automatically build and deploy your site

### Method 2: Netlify CLI

1. **Install Netlify CLI**:
   ```bash
   npm install -g netlify-cli
   ```

2. **Login to Netlify**:
   ```bash
   netlify login
   ```

3. **Build and Deploy**:
   ```bash
   npm run build
   netlify deploy --prod --dir=out
   ```

   Or use the shortcut script:
   ```bash
   npm run deploy
   ```

### Method 3: Drag and Drop

1. **Build the project locally**:
   ```bash
   npm run build
   ```

2. **Drag and drop the `out` folder** to Netlify's deploy interface

## Configuration Files

The following files have been configured for Netlify deployment:

- **`netlify.toml`**: Netlify configuration with build settings and redirects
- **`next.config.js`**: Updated for static export with image optimization disabled
- **`package.json`**: Added deployment scripts

## Environment Variables

If your app uses environment variables:

1. Go to your Netlify site dashboard
2. Navigate to "Site settings" > "Environment variables"
3. Add your variables (e.g., `NEXT_PUBLIC_API_URL`)

## Custom Domain

To use a custom domain:

1. Go to "Site settings" > "Domain management"
2. Add your custom domain
3. Configure DNS settings as instructed by Netlify

## Troubleshooting

### Build Errors
- Check the build logs in Netlify dashboard
- Ensure all dependencies are in `package.json`
- Verify Node.js version compatibility

### 404 Errors
- The `netlify.toml` includes redirects for client-side routing
- Ensure all routes are properly configured in your Next.js app

### Performance Issues
- Static assets are cached for 1 year
- Consider enabling Netlify's asset optimization features

## Post-Deployment

After successful deployment:

1. **Test your site** thoroughly
2. **Set up monitoring** (Netlify Analytics)
3. **Configure forms** (if using Netlify Forms)
4. **Set up redirects** for any legacy URLs

## Continuous Deployment

With Git integration, your site will automatically redeploy when you:
- Push to the main branch
- Merge pull requests
- Update environment variables

Your L2 Boss Timer is now ready for deployment! 🚀
