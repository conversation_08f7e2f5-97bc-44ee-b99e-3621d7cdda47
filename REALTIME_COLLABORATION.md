# Real-Time Collaboration Feature

## Overview

The L2M Boss Timer now supports **real-time collaborative sharing** using WebRTC peer-to-peer connections. This allows multiple users to share boss timers that update live without needing to import/export data.

## Features

### ✨ **Live Sharing**
- **Real-time synchronization** - Timer changes appear instantly for all connected users
- **No import/export needed** - Everything syncs automatically
- **Peer-to-peer connection** - Direct connection between users, no server required
- **Works with static hosting** - Compatible with Netlify, GitHub Pages, etc.

### 🏠 **Room System**
- **Create rooms** with simple 6-character codes (e.g., "ABC123")
- **Join rooms** by entering the room code
- **Host/Member roles** - Room creator becomes the host
- **Connection status** - See who's connected in real-time

### 👥 **User Management**
- **User identification** - Each user has a name and color
- **Activity tracking** - See who made what changes and when
- **Connection indicators** - Visual status of connected users
- **Last action display** - Shows recent timer changes by other users

### 🔄 **Timer Synchronization**
- **Start/Stop/Reset timers** - All actions sync across users
- **Manual time setting** - Custom kill times sync properly
- **Conflict resolution** - Newer changes take precedence
- **Auto-sync on join** - New users get current timer state

## How to Use

### Creating a Room

1. Click the **"Live Share"** button (orange button with lightning icon)
2. Select **"Create Room"** tab
3. Enter your name
4. Click **"Create Room"**
5. Share the generated room code with others

### Joining a Room

1. Click the **"Live Share"** button
2. Select **"Join Room"** tab
3. Enter your name
4. Enter the room code shared with you
5. Click **"Join Room"**

### Using Real-Time Features

Once connected:
- **Timer changes sync automatically** - Start, stop, or reset any timer
- **See connected users** - View who's online in the connection status panel
- **Track recent activity** - See who made the last timer change
- **Leave anytime** - Click "Leave Room" to disconnect

## Technical Details

### WebRTC P2P Architecture
- **No backend server required** - Direct peer-to-peer connections
- **STUN servers** - Uses Google's public STUN servers for NAT traversal
- **Data channels** - Reliable message delivery between peers
- **Connection recovery** - Automatic reconnection on network issues

### Data Synchronization
- **Message-based protocol** - Structured messages for different actions
- **Timestamp-based conflict resolution** - Newer changes win
- **State synchronization** - New users receive full timer state
- **Persistent storage** - Room info saved in localStorage for recovery

### Browser Compatibility
- **Modern browsers** - Requires WebRTC support (Chrome, Firefox, Safari, Edge)
- **HTTPS required** - WebRTC needs secure context in production
- **Mobile friendly** - Works on mobile browsers with WebRTC support

## Message Types

The system uses these message types for synchronization:

- `timer_start` - Boss timer started
- `timer_stop` - Boss timer stopped  
- `timer_reset` - Boss timer reset
- `boss_add` - Custom boss added
- `boss_update` - Boss information updated
- `boss_delete` - Custom boss deleted
- `user_join` - User joined room
- `user_leave` - User left room
- `sync_request` - Request current state
- `sync_response` - Send current state

## Limitations

### Current Limitations
- **Simple signaling** - Uses localStorage for initial connection (works for demo)
- **No persistence** - Room data not saved on server
- **Limited scalability** - Best for small groups (2-10 users)
- **Network dependent** - Requires good internet connection

### Future Improvements
- **Proper signaling server** - For better connection establishment
- **Room persistence** - Save rooms on server
- **User authentication** - Login system for persistent identity
- **Advanced permissions** - Role-based access control
- **Voice chat integration** - Optional voice communication

## Troubleshooting

### Connection Issues
- **Check internet connection** - WebRTC requires stable internet
- **Try different browser** - Some browsers have better WebRTC support
- **Disable VPN** - VPNs can interfere with P2P connections
- **Check firewall** - Ensure WebRTC traffic is allowed

### Sync Issues
- **Refresh page** - Restart the connection
- **Leave and rejoin** - Reset the room connection
- **Check room code** - Ensure correct room code is used
- **Clear browser cache** - Remove old localStorage data

## Privacy & Security

### Data Privacy
- **Local storage only** - Timer data stays in your browser
- **P2P connections** - No data sent to external servers
- **Temporary rooms** - Room codes expire when all users leave
- **No tracking** - No analytics or user tracking

### Security Considerations
- **Room codes** - Keep room codes private
- **Trusted users only** - Only share with people you trust
- **No sensitive data** - Don't include personal information in boss names
- **Browser security** - Keep browser updated for latest security patches

## Development

### File Structure
```
src/
├── hooks/
│   ├── useRealTimeSharing.ts      # WebRTC connection management
│   ├── useCollaborativeTimer.ts   # Enhanced timer with real-time sync
│   └── useTimer.ts                # Original timer (still used for fallback)
├── components/
│   ├── RealTimeShareModal.tsx     # Room creation/joining UI
│   ├── ConnectionStatus.tsx       # Connection status display
│   └── BossTimer.tsx              # Main component (updated)
└── types/
    └── boss.ts                    # Types for real-time features
```

### Key Components
- **useRealTimeSharing** - Manages WebRTC connections and room state
- **useCollaborativeTimer** - Extends timer functionality with real-time sync
- **RealTimeShareModal** - UI for creating/joining rooms
- **ConnectionStatus** - Shows connected users and recent activity

This real-time collaboration feature transforms the boss timer from a single-user tool into a collaborative platform perfect for guild coordination and team hunting!
