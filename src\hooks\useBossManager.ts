import { useState, useEffect, useCallback } from 'react';
import { Boss } from '@/types/boss';
import { bosses as defaultBosses } from '@/data/bosses';

const CUSTOM_BOSSES_STORAGE_KEY = 'custom-bosses';

export function useBossManager() {
  const [customBosses, setCustomBosses] = useState<Boss[]>([]);
  const [isClient, setIsClient] = useState(false);

  // Set client flag after mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load custom bosses from localStorage on mount (client-side only)
  useEffect(() => {
    if (!isClient) return;

    const saved = localStorage.getItem(CUSTOM_BOSSES_STORAGE_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setCustomBosses(parsed);
      } catch (error) {
        console.error('Failed to parse saved custom bosses:', error);
      }
    }
  }, [isClient]);

  // Save custom bosses to localStorage whenever they change (client-side only)
  useEffect(() => {
    if (!isClient) return;
    localStorage.setItem(CUSTOM_BOSSES_STORAGE_KEY, JSON.stringify(customBosses));
  }, [customBosses, isClient]);

  // Get all bosses (default + custom)
  const getAllBosses = useCallback((): Boss[] => {
    return [...defaultBosses, ...customBosses];
  }, [customBosses]);

  // Add a new custom boss
  const addBoss = useCallback((boss: Boss) => {
    setCustomBosses(prev => [...prev, boss]);
  }, []);

  // Update a custom boss
  const updateBoss = useCallback((bossId: string, updates: Partial<Boss>) => {
    setCustomBosses(prev => prev.map(boss => 
      boss.id === bossId ? { ...boss, ...updates } : boss
    ));
  }, []);

  // Delete a custom boss
  const deleteBoss = useCallback((bossId: string) => {
    // Only allow deletion of custom bosses (those with IDs starting with 'custom-')
    if (bossId.startsWith('custom-')) {
      setCustomBosses(prev => prev.filter(boss => boss.id !== bossId));
      return true;
    }
    return false;
  }, []);

  // Check if a boss is custom (can be edited/deleted)
  const isCustomBoss = useCallback((bossId: string): boolean => {
    return bossId.startsWith('custom-');
  }, []);

  // Get a specific boss by ID
  const getBossById = useCallback((bossId: string): Boss | undefined => {
    return getAllBosses().find(boss => boss.id === bossId);
  }, [getAllBosses]);

  return {
    allBosses: getAllBosses(),
    customBosses,
    addBoss,
    updateBoss,
    deleteBoss,
    isCustomBoss,
    getBossById,
  };
}
