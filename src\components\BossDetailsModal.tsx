'use client';

import { <PERSON>, BossDropItem } from '@/types/boss';

interface BossDetailsModalProps {
  boss: <PERSON> | null;
  isOpen: boolean;
  onClose: () => void;
}

const rarityColors = {
  common: 'text-gray-600 bg-gray-100 dark:text-gray-300 dark:bg-gray-700',
  rare: 'text-blue-600 bg-blue-100 dark:text-blue-300 dark:bg-blue-900',
  epic: 'text-purple-600 bg-purple-100 dark:text-purple-300 dark:bg-purple-900',
  legendary: 'text-yellow-600 bg-yellow-100 dark:text-yellow-300 dark:bg-yellow-900',
};



function DropItem({ drop }: { drop: BossDropItem }) {
  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <div className="flex-1">
        <div className="font-medium text-gray-900 dark:text-white">{drop.name}</div>
        <div className="text-sm text-gray-600 dark:text-gray-400 capitalize">{drop.type}</div>
      </div>
      <div className="flex items-center space-x-2">
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${rarityColors[drop.rarity]}`}>
          {drop.rarity}
        </span>
        {drop.dropRate && (
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {drop.dropRate}
          </span>
        )}
      </div>
    </div>
  );
}

export default function BossDetailsModal({ boss, isOpen, onClose }: BossDetailsModalProps) {
  if (!isOpen || !boss) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-2xl">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                {boss.name}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">{boss.location}</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Basic Info */}
            <div className="space-y-6">
              {/* Stats */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Stats</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Level:</span>
                    <span className="ml-2 font-medium text-gray-900 dark:text-white">{boss.level}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">HP:</span>
                    <span className="ml-2 font-medium text-gray-900 dark:text-white">
                      {boss.hp?.toLocaleString() || 'Unknown'}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Respawn:</span>
                    <span className="ml-2 font-medium text-gray-900 dark:text-white">{boss.respawnTime}h</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Players:</span>
                    <span className="ml-2 font-medium text-gray-900 dark:text-white">
                      {boss.minPlayers}-{boss.maxPlayers}
                    </span>
                  </div>
                </div>
              </div>



              {/* Coordinates */}
              {boss.coordinates && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Location</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Coordinates: ({boss.coordinates.x}, {boss.coordinates.y})
                    {boss.coordinates.map && ` - ${boss.coordinates.map}`}
                  </p>
                </div>
              )}

              {/* Description */}
              {boss.description && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Description</h4>
                  <p className="text-gray-600 dark:text-gray-400">{boss.description}</p>
                </div>
              )}

              {/* Strategy */}
              {boss.strategy && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Strategy</h4>
                  <p className="text-gray-600 dark:text-gray-400">{boss.strategy}</p>
                </div>
              )}
            </div>

            {/* Right Column - Drops */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Drops</h4>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {boss.drops.map((drop, index) => (
                  <DropItem key={`${drop.name}-${drop.type}-${index}`} drop={drop} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
