'use client';

import { useState, useEffect } from 'react';
import { useRealTimeSharing } from '@/hooks/useRealTimeSharing';

export default function RealTimeDebug() {
  const [logs, setLogs] = useState<string[]>([]);
  const [testMessage, setTestMessage] = useState('');
  
  const {
    isConnected,
    isHost,
    roomId,
    currentUser,
    connectedUsers,
    connectionStatus,
    error,
    createRoom,
    joinRoom,
    leaveRoom,
    sendMessage,
    addMessageHandler,
  } = useRealTimeSharing();

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  useEffect(() => {
    addLog('RealTimeDebug component mounted');
    
    const removeHandler = addMessageHandler((message) => {
      addLog(`Received message: ${message.type} from ${message.userName}`);
    });

    return removeHandler;
  }, [addMessageHandler]);

  useEffect(() => {
    addLog(`Connection status changed: ${connectionStatus}`);
  }, [connectionStatus]);

  useEffect(() => {
    addLog(`Connected users count: ${connectedUsers.length}`);
  }, [connectedUsers.length]);

  const handleCreateRoom = async () => {
    try {
      addLog('Creating room...');
      const newRoomId = await createRoom('TestUser1');
      addLog(`Room created: ${newRoomId}`);
    } catch (error) {
      addLog(`Failed to create room: ${error}`);
    }
  };

  const handleJoinRoom = async () => {
    if (!roomId) {
      addLog('No room ID available');
      return;
    }
    
    try {
      addLog(`Joining room: ${roomId}...`);
      await joinRoom(roomId, 'TestUser2');
      addLog('Successfully joined room');
    } catch (error) {
      addLog(`Failed to join room: ${error}`);
    }
  };

  const handleSendMessage = () => {
    if (!testMessage.trim()) return;
    
    addLog(`Sending test message: ${testMessage}`);
    sendMessage('sync_request', { testData: testMessage });
    setTestMessage('');
  };

  const handleLeaveRoom = () => {
    addLog('Leaving room...');
    leaveRoom();
    addLog('Left room');
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 space-y-4">
      <h2 className="text-xl font-bold text-gray-900 dark:text-white">Real-Time Debug Panel</h2>
      
      {/* Status */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Status:</span>
          <span className={`px-2 py-1 rounded text-xs ${
            connectionStatus === 'connected' ? 'bg-green-100 text-green-800' :
            connectionStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {connectionStatus}
          </span>
        </div>
        
        {roomId && (
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Room ID:</span>
            <code className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
              {roomId}
            </code>
          </div>
        )}
        
        {currentUser && (
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">User:</span>
            <span className="text-sm">{currentUser.name} ({isHost ? 'Host' : 'Guest'})</span>
          </div>
        )}
        
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Connected Users:</span>
          <span className="text-sm">{connectedUsers.length}</span>
        </div>
        
        {error && (
          <div className="text-red-600 text-sm">{error}</div>
        )}
      </div>

      {/* Actions */}
      <div className="space-y-2">
        {!isConnected ? (
          <div className="flex gap-2">
            <button
              onClick={handleCreateRoom}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Create Test Room
            </button>
            {roomId && (
              <button
                onClick={handleJoinRoom}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Join Room (Simulate 2nd User)
              </button>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            <div className="flex gap-2">
              <input
                type="text"
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                placeholder="Enter test message"
                className="flex-1 px-3 py-2 border border-gray-300 rounded"
              />
              <button
                onClick={handleSendMessage}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Send Test Message
              </button>
            </div>
            <button
              onClick={handleLeaveRoom}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Leave Room
            </button>
          </div>
        )}
      </div>

      {/* Logs */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Debug Logs</h3>
          <button
            onClick={clearLogs}
            className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
          >
            Clear
          </button>
        </div>
        <div className="bg-gray-100 dark:bg-gray-700 rounded p-3 h-64 overflow-y-auto">
          <pre className="text-xs text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
            {logs.join('\n')}
          </pre>
        </div>
      </div>
    </div>
  );
}
