<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Real-Time Sharing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .primary { background-color: #007bff; color: white; }
        .secondary { background-color: #6c757d; color: white; }
        .success { background-color: #28a745; color: white; }
        .danger { background-color: #dc3545; color: white; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .room-list {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .room-item {
            background: white;
            border: 1px solid #ddd;
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            cursor: pointer;
        }
        .room-item:hover {
            background-color: #e9ecef;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Real-Time Sharing Test</h1>
    
    <div class="instructions">
        <h3>Test Instructions:</h3>
        <ol>
            <li><strong>Single Browser Test:</strong> Create a room in the left panel, then join it in the right panel</li>
            <li><strong>Multi-Browser Test:</strong> Open this page in two different browser windows</li>
            <li>Create a room in one window, copy the Room ID</li>
            <li>Join the room in the other window using the Room ID</li>
            <li>Test sending messages between the windows</li>
        </ol>
    </div>

    <div class="two-column">
        <!-- Left Panel - Host -->
        <div class="container">
            <h2>Panel A (Host)</h2>
            
            <div id="statusA" class="status disconnected">Disconnected</div>
            
            <div id="roomInfoA" style="display: none;">
                <p><strong>Room ID:</strong> <span id="roomIdA"></span></p>
                <p><strong>User:</strong> <span id="userNameA"></span></p>
                <p><strong>Connected Users:</strong> <span id="connectedUsersA"></span></p>
            </div>

            <div id="disconnectedActionsA">
                <input type="text" id="createUserNameA" placeholder="Enter your username" value="TestUser1" />
                <button class="primary" onclick="createRoomA()">Create Room</button>
            </div>

            <div id="connectedActionsA" style="display: none;">
                <input type="text" id="messageTextA" placeholder="Enter test message" />
                <button class="primary" onclick="sendTestMessageA()">Send Test Message</button>
                <button class="secondary" onclick="leaveRoomA()">Leave Room</button>
            </div>

            <div>
                <h4>Message Log A</h4>
                <div id="messageLogA" class="log"></div>
                <button onclick="clearLogA()">Clear Log</button>
            </div>
        </div>

        <!-- Right Panel - Guest -->
        <div class="container">
            <h2>Panel B (Guest)</h2>
            
            <div id="statusB" class="status disconnected">Disconnected</div>
            
            <div id="roomInfoB" style="display: none;">
                <p><strong>Room ID:</strong> <span id="roomIdB"></span></p>
                <p><strong>User:</strong> <span id="userNameB"></span></p>
                <p><strong>Connected Users:</strong> <span id="connectedUsersB"></span></p>
            </div>

            <div id="disconnectedActionsB">
                <input type="text" id="joinRoomIdB" placeholder="Enter Room ID" />
                <input type="text" id="joinUserNameB" placeholder="Enter your username" value="TestUser2" />
                <button class="primary" onclick="joinRoomB()">Join Room</button>
                
                <div class="room-list">
                    <h5>Available Rooms:</h5>
                    <div id="availableRoomsB">No rooms found</div>
                    <button class="secondary" onclick="refreshRoomsB()">Refresh</button>
                </div>
            </div>

            <div id="connectedActionsB" style="display: none;">
                <input type="text" id="messageTextB" placeholder="Enter test message" />
                <button class="primary" onclick="sendTestMessageB()">Send Test Message</button>
                <button class="secondary" onclick="leaveRoomB()">Leave Room</button>
            </div>

            <div>
                <h4>Message Log B</h4>
                <div id="messageLogB" class="log"></div>
                <button onclick="clearLogB()">Clear Log</button>
            </div>
        </div>
    </div>

    <script>
        // Panel A (Host) state
        let stateA = {
            isConnected: false,
            roomId: null,
            currentUser: null,
            connectedUsers: []
        };

        // Panel B (Guest) state
        let stateB = {
            isConnected: false,
            roomId: null,
            currentUser: null,
            connectedUsers: []
        };

        function logA(message) {
            const logElement = document.getElementById('messageLogA');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function logB(message) {
            const logElement = document.getElementById('messageLogB');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateUIA() {
            const statusElement = document.getElementById('statusA');
            const roomInfoElement = document.getElementById('roomInfoA');
            const disconnectedActions = document.getElementById('disconnectedActionsA');
            const connectedActions = document.getElementById('connectedActionsA');

            if (stateA.isConnected) {
                statusElement.textContent = 'Connected';
                statusElement.className = 'status connected';
                roomInfoElement.style.display = 'block';
                disconnectedActions.style.display = 'none';
                connectedActions.style.display = 'block';
                
                document.getElementById('roomIdA').textContent = stateA.roomId;
                document.getElementById('userNameA').textContent = stateA.currentUser?.name || '';
                document.getElementById('connectedUsersA').textContent = stateA.connectedUsers.length;
            } else {
                statusElement.textContent = 'Disconnected';
                statusElement.className = 'status disconnected';
                roomInfoElement.style.display = 'none';
                disconnectedActions.style.display = 'block';
                connectedActions.style.display = 'none';
            }
        }

        function updateUIB() {
            const statusElement = document.getElementById('statusB');
            const roomInfoElement = document.getElementById('roomInfoB');
            const disconnectedActions = document.getElementById('disconnectedActionsB');
            const connectedActions = document.getElementById('connectedActionsB');

            if (stateB.isConnected) {
                statusElement.textContent = 'Connected';
                statusElement.className = 'status connected';
                roomInfoElement.style.display = 'block';
                disconnectedActions.style.display = 'none';
                connectedActions.style.display = 'block';
                
                document.getElementById('roomIdB').textContent = stateB.roomId;
                document.getElementById('userNameB').textContent = stateB.currentUser?.name || '';
                document.getElementById('connectedUsersB').textContent = stateB.connectedUsers.length;
            } else {
                statusElement.textContent = 'Disconnected';
                statusElement.className = 'status disconnected';
                roomInfoElement.style.display = 'none';
                disconnectedActions.style.display = 'block';
                connectedActions.style.display = 'none';
            }
        }

        function createRoomA() {
            const userName = document.getElementById('createUserNameA').value.trim();
            if (!userName) {
                alert('Please enter a username');
                return;
            }

            // Simulate room creation
            stateA.roomId = Math.random().toString(36).substring(2, 8).toUpperCase();
            stateA.currentUser = { id: Math.random().toString(36).substring(2, 15), name: userName };
            stateA.connectedUsers = [stateA.currentUser];
            stateA.isConnected = true;

            // Store in localStorage using the same format as the real app
            const roomData = {
                users: stateA.connectedUsers,
                signals: [],
                lastUpdated: Date.now()
            };
            
            localStorage.setItem(`realtime-room-${stateA.roomId}`, JSON.stringify(roomData));
            
            // Also store in room list
            const roomList = localStorage.getItem('active-rooms') || '{}';
            const rooms = JSON.parse(roomList);
            rooms[stateA.roomId] = {
                ...roomData,
                lastActivity: Date.now()
            };
            localStorage.setItem('active-rooms', JSON.stringify(rooms));

            logA(`Room created: ${stateA.roomId}`);
            logA(`User ${userName} joined as host`);
            updateUIA();
            
            // Auto-fill the join room field in panel B
            document.getElementById('joinRoomIdB').value = stateA.roomId;
            refreshRoomsB();
        }

        function joinRoomB() {
            const joinRoomId = document.getElementById('joinRoomIdB').value.trim();
            const userName = document.getElementById('joinUserNameB').value.trim();
            
            if (!joinRoomId || !userName) {
                alert('Please enter both Room ID and username');
                return;
            }

            // Check if room exists using the same logic as the real app
            const roomData = localStorage.getItem(`realtime-room-${joinRoomId}`);
            if (!roomData) {
                alert('Room not found. Make sure the room ID is correct and the host has created the room.');
                logB(`Failed to join room: ${joinRoomId} - Room not found`);
                return;
            }

            try {
                const parsedRoomData = JSON.parse(roomData);
                stateB.roomId = joinRoomId;
                stateB.currentUser = { id: Math.random().toString(36).substring(2, 15), name: userName };
                stateB.connectedUsers = [...parsedRoomData.users, stateB.currentUser];
                stateB.isConnected = true;

                // Update room data with new user
                const updatedRoomData = {
                    users: stateB.connectedUsers,
                    signals: parsedRoomData.signals || [],
                    lastUpdated: Date.now()
                };
                localStorage.setItem(`realtime-room-${joinRoomId}`, JSON.stringify(updatedRoomData));

                logB(`Joined room: ${joinRoomId}`);
                logB(`User ${userName} joined`);
                updateUIB();
                
                // Update Panel A if it's the same room
                if (stateA.roomId === joinRoomId) {
                    stateA.connectedUsers = stateB.connectedUsers;
                    updateUIA();
                    logA(`User ${userName} joined the room`);
                }
            } catch (error) {
                alert('Failed to join room');
                logB(`Error joining room: ${error.message}`);
            }
        }

        function refreshRoomsB() {
            const roomList = localStorage.getItem('active-rooms');
            const container = document.getElementById('availableRoomsB');
            
            if (!roomList) {
                container.innerHTML = 'No rooms found';
                return;
            }

            try {
                const rooms = JSON.parse(roomList);
                const now = Date.now();
                const oneHourAgo = now - 60 * 60 * 1000;
                
                const activeRooms = Object.entries(rooms)
                    .filter(([_, roomData]) => roomData.lastActivity > oneHourAgo)
                    .map(([roomId, roomData]) => ({
                        id: roomId,
                        userCount: roomData.users?.length || 0,
                        host: roomData.users?.[0]?.name || 'Unknown'
                    }));

                if (activeRooms.length === 0) {
                    container.innerHTML = 'No active rooms found';
                    return;
                }

                container.innerHTML = activeRooms.map(room => 
                    `<div class="room-item" onclick="document.getElementById('joinRoomIdB').value='${room.id}'">
                        <strong>${room.id}</strong> - ${room.userCount} user(s) - Host: ${room.host}
                    </div>`
                ).join('');
            } catch (error) {
                container.innerHTML = 'Error loading rooms';
                logB(`Error loading rooms: ${error.message}`);
            }
        }

        function sendTestMessageA() {
            const messageText = document.getElementById('messageTextA').value.trim();
            if (!messageText) {
                alert('Please enter a message');
                return;
            }

            logA(`Sending message: "${messageText}"`);
            logB(`Received from ${stateA.currentUser.name}: "${messageText}"`);
            document.getElementById('messageTextA').value = '';
        }

        function sendTestMessageB() {
            const messageText = document.getElementById('messageTextB').value.trim();
            if (!messageText) {
                alert('Please enter a message');
                return;
            }

            logB(`Sending message: "${messageText}"`);
            logA(`Received from ${stateB.currentUser.name}: "${messageText}"`);
            document.getElementById('messageTextB').value = '';
        }

        function leaveRoomA() {
            if (stateA.roomId && stateA.currentUser) {
                logA(`User ${stateA.currentUser.name} left room ${stateA.roomId}`);
            }
            stateA = { isConnected: false, roomId: null, currentUser: null, connectedUsers: [] };
            updateUIA();
        }

        function leaveRoomB() {
            if (stateB.roomId && stateB.currentUser) {
                logB(`User ${stateB.currentUser.name} left room ${stateB.roomId}`);
            }
            stateB = { isConnected: false, roomId: null, currentUser: null, connectedUsers: [] };
            updateUIB();
        }

        function clearLogA() {
            document.getElementById('messageLogA').textContent = '';
        }

        function clearLogB() {
            document.getElementById('messageLogB').textContent = '';
        }

        // Initialize
        updateUIA();
        updateUIB();
        refreshRoomsB();
        
        logA('Panel A ready - Create a room to start');
        logB('Panel B ready - Join a room to start');
        
        // Auto-refresh rooms every 5 seconds
        setInterval(refreshRoomsB, 5000);
    </script>
</body>
</html>
