'use client';

import { useState, useEffect, useCallback } from 'react';
import { NotificationSettings } from '@/types/boss';

const NOTIFICATION_SETTINGS_KEY = 'boss-notification-settings';

const defaultSettings: NotificationSettings = {
  enabled: true,
  warningMinutes: [30, 10, 5],
  sound: true,
  desktop: true,
};

export function useNotifications() {
  const [settings, setSettings] = useState<NotificationSettings>(defaultSettings);
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [isClient, setIsClient] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioInterval, setAudioInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load settings from localStorage
  useEffect(() => {
    if (!isClient) return;

    const saved = localStorage.getItem(NOTIFICATION_SETTINGS_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setSettings({ ...defaultSettings, ...parsed });
      } catch (error) {
        console.error('Failed to parse notification settings:', error);
      }
    }

    // Check notification permission
    if ('Notification' in window) {
      setPermission(Notification.permission);
    }
  }, [isClient]);

  // Save settings to localStorage
  useEffect(() => {
    if (!isClient) return;
    localStorage.setItem(NOTIFICATION_SETTINGS_KEY, JSON.stringify(settings));
  }, [settings, isClient]);

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (audioInterval) {
        clearInterval(audioInterval);
      }
    };
  }, [audioInterval]);

  const requestPermission = useCallback(async () => {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const result = await Notification.requestPermission();
    setPermission(result);
    return result === 'granted';
  }, []);

  const showNotification = useCallback((title: string, options?: NotificationOptions) => {
    if (!settings.enabled || !settings.desktop) return;

    if (Notification.permission === 'granted') {
      const notification = new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        ...options,
      });

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      return notification;
    }
  }, [settings]);

  const playNotificationSound = useCallback(() => {
    if (!settings.enabled || !settings.sound) return;

    // Stop any currently playing sound
    if (audioInterval) {
      clearInterval(audioInterval);
      setAudioInterval(null);
    }

    setIsPlaying(true);

    const playBeep = () => {
      try {
        const AudioContextClass = window.AudioContext || (window as typeof window & { webkitAudioContext: typeof AudioContext }).webkitAudioContext;
        const audioContext = new AudioContextClass();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // Make it louder and more attention-grabbing
        oscillator.frequency.value = 1000; // Higher frequency
        oscillator.type = 'square'; // More piercing sound

        // Louder volume (0.7 instead of 0.3)
        gainNode.gain.setValueAtTime(0.7, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.8);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.8);
      } catch (error) {
        console.warn('Could not play notification sound:', error);
      }
    };

    // Play immediately
    playBeep();

    // Then repeat every 2 seconds
    const interval = setInterval(playBeep, 2000);
    setAudioInterval(interval);

    // Auto-stop after 30 seconds if not manually stopped
    setTimeout(() => {
      if (interval) {
        clearInterval(interval);
        setAudioInterval(null);
        setIsPlaying(false);
      }
    }, 30000);
  }, [settings, audioInterval]);

  const stopNotificationSound = useCallback(() => {
    if (audioInterval) {
      clearInterval(audioInterval);
      setAudioInterval(null);
    }
    setIsPlaying(false);
  }, [audioInterval]);

  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  }, []);

  return {
    settings,
    permission,
    requestPermission,
    showNotification,
    playNotificationSound,
    stopNotificationSound,
    isPlaying,
    updateSettings,
  };
}
