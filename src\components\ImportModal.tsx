'use client';

import { useState } from 'react';
import { Boss } from '@/types/boss';
import { useSharing } from '@/hooks/useSharing';

interface ImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (data: {
    customBosses: Boss[];
    timerStates: Record<string, { isActive: boolean; [key: string]: unknown }>;
    sharedBy?: string;
    sharedAt: string;
  }) => void;
}

export default function ImportModal({ isOpen, onClose, onImport }: ImportModalProps) {
  const [shareCode, setShareCode] = useState('');
  const [password, setPassword] = useState('');
  const [importMode, setImportMode] = useState<'merge' | 'replace'>('merge');
  const [previewData, setPreviewData] = useState<{
    customBosses: Boss[];
    timerStates: Record<string, { isActive: boolean; [key: string]: unknown }>;
    sharedBy?: string;
    sharedAt: string;
  } | null>(null);
  
  const { 
    isImporting, 
    error, 
    parseShareData, 
    clearError 
  } = useSharing();

  const handleParseCode = () => {
    clearError();
    
    if (!shareCode.trim()) {
      return;
    }

    // Extract share code from URL if it's a full URL
    let code = shareCode.trim();
    if (code.includes('?share=')) {
      const urlParams = new URLSearchParams(code.split('?')[1]);
      code = urlParams.get('share') || '';
    }

    const data = parseShareData(code, password || undefined);
    if (data) {
      setPreviewData(data);
    }
  };

  const handleImport = () => {
    if (previewData) {
      onImport({
        customBosses: previewData.customBosses,
        timerStates: previewData.timerStates,
        sharedBy: previewData.sharedBy,
        sharedAt: previewData.sharedAt,
      });
      handleClose();
    }
  };

  const handleClose = () => {
    setShareCode('');
    setPassword('');
    setImportMode('merge');
    setPreviewData(null);
    clearError();
    onClose();
  };

  const getPreviewStats = () => {
    if (!previewData) return null;
    
    const activeBosses = Object.values(previewData.timerStates).filter((state) => state.isActive).length;
    const totalBosses = previewData.customBosses.length + Object.keys(previewData.timerStates).length;
    
    return {
      activeBosses,
      totalBosses,
      customBosses: previewData.customBosses.length,
      sharedBy: previewData.sharedBy,
      sharedAt: new Date(previewData.sharedAt).toLocaleString(),
    };
  };

  if (!isOpen) return null;

  const stats = getPreviewStats();

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      {/* Background overlay */}
      <div 
        className="absolute inset-0"
        onClick={handleClose}
      />

      {/* Modal panel */}
      <div className="relative w-full max-w-2xl bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
            Import Boss Timers
          </h3>
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {!previewData ? (
          /* Import Configuration */
          <div className="space-y-4">
            {/* Share Code Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Share Link or Code
              </label>
              <textarea
                value={shareCode}
                onChange={(e) => setShareCode(e.target.value)}
                placeholder="Paste the share link or code here..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            {/* Password Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Password (if required)
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password if the share is protected"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            {/* Error Display */}
            {error && (
              <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded">
                {error}
              </div>
            )}

            {/* Parse Button */}
            <button
              onClick={handleParseCode}
              disabled={isImporting || !shareCode.trim()}
              className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors flex items-center justify-center gap-2"
            >
              {isImporting ? (
                <>
                  <svg className="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Parsing...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Preview Import
                </>
              )}
            </button>
          </div>
        ) : (
          /* Import Preview */
          <div className="space-y-4">
            {/* Success Message */}
            <div className="bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded">
              <div className="flex items-center gap-2 mb-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="font-medium">Share data parsed successfully!</span>
              </div>
              <p className="text-sm">
                Review the data below and choose how to import it.
              </p>
            </div>

            {/* Share Info */}
            {stats && (
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Share Information:
                </h4>
                <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                  {stats.sharedBy && (
                    <div>Shared by: <span className="font-medium">{stats.sharedBy}</span></div>
                  )}
                  <div>Shared at: <span className="font-medium">{stats.sharedAt}</span></div>
                </div>
              </div>
            )}

            {/* Preview Stats */}
            {stats && (
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  What will be imported:
                </h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {stats.totalBosses}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Total Bosses</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {stats.activeBosses}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Active Timers</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {stats.customBosses}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Custom Bosses</div>
                  </div>
                </div>
              </div>
            )}

            {/* Import Mode */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Import Mode
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="merge"
                    checked={importMode === 'merge'}
                    onChange={(e) => setImportMode(e.target.value as 'merge')}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    <strong>Merge:</strong> Add new bosses and update existing timers
                  </span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="replace"
                    checked={importMode === 'replace'}
                    onChange={(e) => setImportMode(e.target.value as 'replace')}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    <strong>Replace:</strong> Replace all current data with imported data
                  </span>
                </label>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <button
                onClick={() => setPreviewData(null)}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button
                onClick={handleImport}
                className="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
              >
                Import Data
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
