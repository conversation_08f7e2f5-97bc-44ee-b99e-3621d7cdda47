"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/build/content/next-shims/telemetry-storage.cts
var telemetry_storage_exports = {};
__export(telemetry_storage_exports, {
  TelemetryShim: () => TelemetryShim
});
module.exports = __toCommonJS(telemetry_storage_exports);
var TelemetryShim = class {
  sessionId = "shim";
  get anonymousId() {
    return "shim";
  }
  get salt() {
    return "shim";
  }
  setEnabled() {
    return null;
  }
  get isEnabled() {
    return false;
  }
  oneWayHash() {
    return "shim";
  }
  record() {
    return Promise.resolve({ isFulfilled: true, isRejected: false });
  }
  flush() {
    return Promise.resolve(null);
  }
  flushDetached() {
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  TelemetryShim
});
